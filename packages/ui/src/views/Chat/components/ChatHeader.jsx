import { useSidebar, useFlows } from '@/views/Chat/hooks/ChatHook'
import { AppBar, IconButton, Stack, Toolbar, Tooltip, Typography } from '@mui/material'
import { IconEdit, IconLayoutSidebar } from '@tabler/icons-react'
import clsx from 'clsx'
import { memo } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import LanguageSwitcher from '../../../components/LanguageSwitcher'

const ChatHeader = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { t } = useTranslation()

  const { chatFlowId, chatSessionId } = useParams()

  const user = useSelector((state) => state.user)
  const [sidebar, setSidebar] = useSidebar()
  const [chatFlows] = useFlows()

  // Get page title based on current route
  const getPageTitle = () => {
    if (location.pathname === '/chat/translation') {
      return t('header.translation')
    }
    if (chatFlowId) {
      const currentFlow = chatFlows?.find((flow) => flow.id === chatFlowId)
      return currentFlow?.name || t('header.aiAgent')
    }
    return t('header.chatAI')
  }

  const isChatbotMode = chatFlowId && location.pathname !== '/chat/translation'

  return (
    <AppBar
      position='static'
      open={sidebar.open}
      sx={{
        background: isChatbotMode ? 'transparent' : '#ffffff',
        boxShadow: 'none',
        borderBottom: isChatbotMode ? 'none' : '1px solid #0d0d0d0d',
        zIndex: isChatbotMode ? 1300 : 'auto'
      }}
      className={`${isChatbotMode ? 'fixed w-[50%]' : 'block w-full'}`}
    >
      <Toolbar className='p-2 h-[50px] flex items-center justify-start'>
        <Stack gap={1} direction='row' alignItems='center'>
          <Tooltip title={sidebar.open ? t('sidebar.closeSidebar') : t('sidebar.openSidebar')}>
            <IconButton color='inherit' onClick={() => setSidebar({ open: !sidebar.open })}>
              <IconLayoutSidebar color={isChatbotMode ? '#ffffff' : '#5d5d5d'} />
            </IconButton>
          </Tooltip>
          {/* <Tooltip title={t('sidebar.newChat')} className={clsx((sidebar.open || !chatFlowId) && 'hidden')}>
            <IconButton color='inherit' onClick={() => chatSessionId && navigate(`/chat/${chatFlowId}`)}>
              <IconEdit color={isChatbotMode ? '#ffffff' : '#5d5d5d'} />
            </IconButton>
          </Tooltip> */}
        </Stack>
        <div className='flex-1' />
        <Stack className={`${isChatbotMode ? 'hidden' : 'flex'}`} direction='row' justifyContent='center' alignItems='center' spacing={2}>
          <Typography color={isChatbotMode ? '#ffffff' : '#333'}>{getPageTitle()}</Typography>
          <LanguageSwitcher />
          <Typography color={isChatbotMode ? '#ffffff' : '#5d5d5d'}>{user?.username || ''}</Typography>
        </Stack>
      </Toolbar>
    </AppBar>
  )
}

export default memo(ChatHeader)
