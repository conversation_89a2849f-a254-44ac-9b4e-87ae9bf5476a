{"tabs": {"text": "<PERSON><PERSON><PERSON>", "document": "<PERSON><PERSON><PERSON> l<PERSON>"}, "languages": {"vietnamese": "Việt", "english": "<PERSON><PERSON>", "japanese": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "Trung", "detectLanguage": "<PERSON><PERSON><PERSON> hi<PERSON> ngôn ngữ"}, "translation": {"translating": "<PERSON><PERSON> d<PERSON>ch...", "cannotTranslate": "<PERSON><PERSON><PERSON><PERSON> thể dịch v<PERSON><PERSON> bản", "translationWillAppear": "<PERSON><PERSON><PERSON> dịch sẽ xuất hiện ở đây", "processingFile": "<PERSON><PERSON> lý file...", "splittingFile": "<PERSON><PERSON> chia nhỏ file để xử lý...", "translatingParts": "<PERSON><PERSON> d<PERSON>ch {{count}} phần của file", "translatedParts": "<PERSON><PERSON> dịch {{completed}}/{{total}} phần...", "partsCompleted": "{{processed}}/{{total}} phần đã hoàn thành ({{progress}}%)", "noPartsTranslated": "<PERSON><PERSON><PERSON><PERSON> có phần n<PERSON>o đ<PERSON><PERSON><PERSON> dịch thành công", "translationError": "<PERSON><PERSON> xảy ra lỗi khi dịch văn bản"}, "alerts": {"translatingWait": "<PERSON><PERSON> dịch. <PERSON><PERSON> lòng đợi hoàn thành trước khi đổi ngôn ngữ."}, "phonetic": {"hide": "Ẩn {{type}}", "show": "Hiện {{type}}", "pinyin": "<PERSON><PERSON><PERSON>", "romaji": "<PERSON><PERSON>", "phonetic": "Phonetic"}, "sidebar": {"translation": "<PERSON><PERSON><PERSON>", "openSidebar": "Mở sidebar", "closeSidebar": "Đóng sidebar", "searchAgent": "<PERSON><PERSON><PERSON>", "newChat": "Đoạn chat mới"}, "header": {"translation": "<PERSON><PERSON><PERSON>", "aiAgent": "AI Agent", "chatAI": "Chat AI"}, "welcome": {"title": "<PERSON><PERSON>o mừng đến v<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> một dịch vụ từ sidebar để bắt đầu:", "translationDesc": "<PERSON><PERSON><PERSON> văn bản và tài liệu gi<PERSON>a các ngôn ngữ", "aiAgentsDesc": "<PERSON><PERSON><PERSON> chuy<PERSON>n với các AI agent ch<PERSON><PERSON><PERSON>"}, "menu": {"rename": "<PERSON><PERSON><PERSON> tên", "delete": "Xóa", "deleteChat": "<PERSON><PERSON>a đoạn chat?"}, "search": {"searchAgent": "<PERSON><PERSON><PERSON>"}, "context": {"welcomeMessage": "<PERSON><PERSON> chào, tôi có thể giúp gì cho bạn?"}}