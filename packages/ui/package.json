{"name": "flowise-ui", "version": "2.2.1", "license": "SEE LICENSE IN LICENSE.md", "homepage": "https://flowiseai.com", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {"@codemirror/lang-javascript": "6.2.1", "@codemirror/lang-json": "6.0.1", "@codemirror/view": "6.22.3", "@emotion/cache": "^11.4.0", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@microsoft/fetch-event-source": "^2.0.1", "@mui/base": "5.0.0-beta.40", "@mui/icons-material": "5.0.3", "@mui/lab": "5.0.0-alpha.156", "@mui/material": "5.15.0", "@mui/x-data-grid": "6.8.0", "@mui/x-date-pickers": "^5.0.20", "@tabler/icons-react": "^3.3.0", "@uiw/codemirror-theme-sublime": "^4.21.21", "@uiw/codemirror-theme-vscode": "^4.21.21", "@uiw/react-codemirror": "^4.21.21", "ahooks": "^3.8.4", "axios": "1.6.2", "chart.js": "^4.4.8", "chartjs-plugin-datalabels": "^2.2.0", "chartjs-plugin-zoom": "^2.2.0", "clsx": "^1.1.1", "cmcts-c-agent-embedding": "1.0.46-vib", "cmcts-c-agent-embedding-react": "1.0.46-vib", "compromise": "^14.14.4", "dccxx-s3-explorer": "1.0.1-vib", "dotenv": "^16.0.0", "elkjs": "^0.9.3", "fast-xml-parser": "^5.2.5", "file-saver": "^2.0.5", "flowise-react-json-view": "*", "formik": "^2.2.6", "framer-motion": "^4.1.13", "history": "^5.0.0", "html-react-parser": "^3.0.4", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "jszip": "^3.10.1", "localforage": "^1.10.0", "lodash": "^4.17.21", "mammoth": "^1.7.2", "moment": "^2.29.3", "notistack": "^2.0.4", "pinyin": "^4.0.0", "prop-types": "^15.7.2", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-code-blocks": "^0.0.9-0", "react-color": "^2.19.3", "react-datepicker": "^4.21.0", "react-device-detect": "^1.17.0", "react-dom": "^18.2.0", "react-fast-compare": "^3.2.2", "react-i18next": "^15.5.3", "react-markdown": "^8.0.6", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.0.5", "react-router": "^6.30.0", "react-router-dom": "^6.30.0", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.5.6", "redux": "^4.0.5", "rehype-mathjax": "^4.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "socket.io-client": "^4.6.1", "uuid": "^9.0.1", "wanakana": "^5.3.1", "xlsx": "^0.18.5", "yup": "^0.32.9"}, "scripts": {"dev": "vite --no-open", "start": "vite", "build": "vite build", "clean": "<PERSON><PERSON><PERSON> build", "nuke": "rimraf build node_modules .turbo"}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/eslint-parser": "^7.15.8", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/jest-dom": "^5.11.10", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^12.8.3", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "pretty-quick": "^3.1.3", "react-scripts": "^5.0.1", "rimraf": "^5.0.5", "sass": "^1.42.1", "tailwindcss": "^3.4.16", "typescript": "^5.4.5", "vite": "^5.0.2", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-pwa": "^0.17.0", "vite-plugin-react-js-support": "^1.0.7"}}